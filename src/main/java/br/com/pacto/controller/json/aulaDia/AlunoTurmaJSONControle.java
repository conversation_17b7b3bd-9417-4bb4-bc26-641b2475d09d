/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.programa.ObjetivoAlunoDTO;
import br.com.pacto.bean.programa.ObjetivoIntermediarioAlunoDTO;
import br.com.pacto.controller.json.agendamento.AgendaController;
import br.com.pacto.controller.json.agendamento.AgendamentoPersonalDTO;
import br.com.pacto.controller.json.agendamento.FiltrosAgendamentosDTO;
import br.com.pacto.controller.json.agendamento.PeriodoFiltrarEnum;
import br.com.pacto.controller.json.avaliacao.AvaliacaoJSONControle;
import br.com.pacto.controller.json.avaliacao.SugestaoHorarioPersonalJSON;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.HorarioConcomitanteException;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.agenda.DisponibilidadeService;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.swagger.respostas.categoria.atividade.ExemploRespostaListCategoriaAtividadeResponseTOPaginacao;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaConsultarAulas;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaConsultarAulasDesmarcadas;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaDesmarcarAula;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaConsultarAulasModalidadeAluno;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaConsultarTurmasAluno;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaAlunosTurma;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaMarcarAula;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaExtrato;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaSaldoAluno;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaSaldoAlunoReporEMarcar;
import br.com.pacto.swagger.respostas.objetivos.ExemploRespostaListObjetivoAlunoVO;
import br.com.pacto.swagger.respostas.objetivos.ExemploRespostaObjetivoAlunoVO;
import br.com.pacto.swagger.respostas.objetivos.ExemploRespostaObjetivoIntermediarioAlunoVO;
import br.com.pacto.swagger.respostas.objetivos.ExemploRespostaRemoverObjetivoIntermediario;
import br.com.pacto.swagger.respostas.colaborador.ExemploRespostaSucesso;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaAulasConfirmadasPaginacao;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaMarcarEuQueroHorario;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaDesmarcarEuQueroHorario;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaConsultarTurmasDisponiveisApp;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaHorariosSugeridos;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaDisponibilidades;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaDisponibilidadesAgrupadasPorComportamento;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaCriarAgendamento;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaRemoverAgendamento;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaAlterarAgendamento;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaBuscaFrequenciaAluno;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaFrequenciaAluno;
import br.com.pacto.swagger.respostas.alunoturma.ExemploRespostaSaldoAulasColetivas;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.enumeradores.OrigemSistemaEnum;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.json.ControleCreditoTreinoJSON;
import io.swagger.annotations.*;
import springfox.documentation.annotations.ApiIgnore;
import org.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/alunoTurma")
public class AlunoTurmaJSONControle extends SuperControle {

    @Autowired
    private AgendaTotalService agendaService;
    @Autowired
    private AvaliacaoFisicaService avaliacaoService;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private ClienteSinteticoService clienteService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private EmpresaService empService;
    @Autowired
    private TipoEventoService tipoEventoService;
    @Autowired
    private DisponibilidadeService disponibilidadeService;

    @ApiOperation(value = "Consultar aulas do aluno",
            notes = "Consulta as próximas aulas disponíveis para um aluno específico, permitindo filtrar por contrato e definir se devem ser exibidas apenas aulas futuras. Retorna informações detalhadas das aulas incluindo horários, modalidades, professores e disponibilidade de vagas.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de aulas consultada com sucesso",
                    response = ExemploRespostaConsultarAulas.class)
    })
    @RequestMapping(value = "{ctx}/consultarAulas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAulas(@PathVariable String ctx,
                            @ApiParam(value = "Número da matrícula do aluno", required = true, defaultValue = "12345")
                            @RequestParam final Integer matricula,
                            @ApiParam(value = "Código do contrato para filtrar modalidades específicas", defaultValue = "1001")
                            @RequestParam(required = false) final String contrato,
                            @ApiParam(value = "Define se devem ser exibidas apenas aulas futuras (true) ou todas as aulas (false)", required = true, defaultValue = "true")
                            @RequestParam final String aulasFuturas) {
        ModelMap mm = new ModelMap();
        try {
            Integer contratoInt = null;
            try {
                contratoInt = Integer.valueOf(contrato);
            } catch (Exception e) {
                contratoInt = null;
            }
            empService.validarChave(ctx);
            String retorno = agendaService.obterProximasAulasVerificandoModoConsulta(ctx, matricula, Boolean.parseBoolean(aulasFuturas));
            List<Modalidade> listaModalidades = agendaService.todasModalidades(ctx);

            if (retorno.startsWith("ERRO:")) {
                mm.addAttribute("aulas", new JSONArray().toString());
            } else {
                List<AgendaTotalJSON> proximasAulas = JSONMapper.getList(new JSONArray(retorno), AgendaTotalJSON.class);

                if (proximasAulas == null) {
                    proximasAulas = new ArrayList<AgendaTotalJSON>();
                }
                List<AgendaTotalTO> listaTO = new ArrayList<AgendaTotalTO>();
                Integer empresa = !UteisValidacao.emptyList(proximasAulas) ? proximasAulas.get(0).getEmpresa() : null;
                Date inicio = Calendario.hoje();
                Date fim = Uteis.somarDias(inicio, 30);
                if (empresa != null) {
                    for (AgendaTotalJSON a : proximasAulas) {
                        AgendaTotalTO agenda = new AgendaTotalTO(a);
                        listaTO.add(agenda);
                    }
                    agendaService.montarMapaAgendados(ctx, inicio, fim, empresa, listaTO, true);
                }
                List<AulaDiaJSON> jsonArray = new ArrayList<AulaDiaJSON>();
                List<Integer> modalidadeFiltrar = new ArrayList<>();
                if (!UteisValidacao.emptyNumber(contratoInt)) {
                    modalidadeFiltrar = agendaService.modalidadesContrato(ctx, contratoInt, matricula);
                }
                for (AgendaTotalJSON aula : proximasAulas) {
                    if (!UteisValidacao.emptyNumber(contratoInt) && !modalidadeFiltrar.contains(aula.getCodigoTipo())) {
                        continue;
                    }
                    if (!aula.getAulaCheia()) {
                        aula.setNrVagasPreenchidas(aula.getOcupacao());
                        AulaDiaJSON aulaDiaJSON = new AulaDiaJSON(aula);
                        for (Modalidade modalidade : listaModalidades) {
                            if (modalidade.getCodigoZW().equals(aula.getCodigoTipo())) {
                                aulaDiaJSON.setCorModalidade(modalidade.getCor().getCor());
                                break;
                            }
                        }

                        List<TurmaVideoDTO> listaLinkVideosSalvos = agendaService.obterListaTurmaVideo(ctx, aula.getCodigoTurma());
                        aulaDiaJSON.setLinkVideos(listaLinkVideosSalvos);

                        jsonArray.add(aulaDiaJSON);
                    }
                }
                jsonArray = Ordenacao.ordenarLista(jsonArray, "diaDate");
                mm.addAttribute("aulas", jsonArray);
            }
        } catch (Exception ex) {
            mm.addAttribute("aulas", new JSONArray().toString());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Consultar aulas desmarcadas do aluno",
            notes = "Consulta as aulas que foram desmarcadas pelo aluno, permitindo visualizar o histórico de cancelamentos. Retorna informações detalhadas das aulas desmarcadas incluindo horários, modalidades e professores.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de aulas desmarcadas consultada com sucesso",
                    response = ExemploRespostaConsultarAulasDesmarcadas.class)
    })
    @RequestMapping(value = "{ctx}/consultarAulasDesmarcadas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAulasDesmarcadas(@PathVariable String ctx,
                                       @ApiParam(value = "Número da matrícula do aluno", required = true, defaultValue = "12345")
                                       @RequestParam final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            List<AgendaTotalJSON> proximasAulas = agendaService.consultarAulasDesmarcadas(ctx, matricula, null);
            if (proximasAulas == null) {
                proximasAulas = new ArrayList<AgendaTotalJSON>();
            }
            List<AulaDiaJSON> jsonArray = new ArrayList<AulaDiaJSON>();
            for (AgendaTotalJSON aula : proximasAulas) {
                jsonArray.add(new AulaDiaJSON(aula));
            }
            jsonArray = Ordenacao.ordenarLista(jsonArray, "inicio");
            mm.addAttribute("aulas", jsonArray);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Desmarcar aula do aluno",
            notes = "Desmarca uma aula específica do aluno, liberando a vaga para outros alunos. A operação remove o aluno da lista de participantes da aula na data especificada.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Aula desmarcada com sucesso",
                    response = ExemploRespostaDesmarcarAula.class)
    })
    @RequestMapping(value = "{ctx}/desmarcarAula", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap desmarcarAula(@PathVariable String ctx,
                           @ApiParam(value = "Número da matrícula do aluno", required = true, defaultValue = "12345")
                           @RequestParam final Integer matricula,
                           @ApiParam(value = "Código único do horário da turma", required = true, defaultValue = "5001")
                           @RequestParam final Integer codigoHorarioTurma,
                           @ApiParam(value = "Data da aula a ser desmarcada no formato dd/MM/yyyy", required = true, defaultValue = "15/01/2024")
                           @RequestParam final String data,
                           @ApiParam(value = "Código do contrato do aluno", defaultValue = "1001")
                           @RequestParam(required = false) final Integer contrato) {
        ModelMap mm = new ModelMap();
        try {
            String result = agendaService.marcarDesmarcar(ctx, matricula,
                    Uteis.getDate(data, "dd/MM/yyyy"), codigoHorarioTurma, false, false,
                    false, contrato);
            if (result.startsWith("ERRO:")) {
                throw new Exception(result);
            }
            mm.addAttribute(STATUS_SUCESSO, result);
            ConfiguracaoSistema cfTipo = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.ALUNO_MARCAR_PROPRIA_AULA);
            if (cfTipo.getValorAsBoolean()) {
                AgendaTotalService agendaTotalService = (AgendaTotalService) UtilContext.getBean(AgendaTotalService.class);
                agendaTotalService.presencaAlunoAula(ctx, matricula, codigoHorarioTurma, data, null, OrigemSistemaEnum.APP_TREINO, false);
            }
        } catch (Exception ex) {
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
        }
        return mm;
    }

    @ApiOperation(value = "Consultar aulas de modalidade por aluno",
            notes = "Consulta todas as aulas de modalidades que um aluno específico possui agendadas em um período determinado. " +
                    "Retorna informações detalhadas sobre cada aula incluindo horários, professores, modalidades e disponibilidade de vagas. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via código da empresa. " +
                    "<br/><strong>Comportamento:</strong> Busca o aluno pela matrícula, obtém o código da empresa associada e consulta todas as aulas agendadas no período especificado. " +
                    "<br/><strong>Retorno:</strong> Lista de aulas ordenadas por data com informações completas de cada agendamento.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso",
                    response = ExemploRespostaConsultarAulasModalidadeAluno.class)
    })
    @RequestMapping(value = "{ctx}/consultarAulasModalidadeAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAulasModalidadeAluno(@PathVariable String ctx,
                                           @ApiParam(value = "Data de início do período de consulta no formato dd/MM/yyyy",
                                                   required = true, defaultValue = "01/02/2024")
                                           @RequestParam final String inicio,
                                           @ApiParam(value = "Data de fim do período de consulta no formato dd/MM/yyyy",
                                                   required = true, defaultValue = "28/02/2024")
                                           @RequestParam final String fim,
                                           @ApiParam(value = "Número da matrícula do aluno para consulta das aulas",
                                                   required = true, defaultValue = "12345")
                                           @RequestParam final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx,
                    matricula.toString());
            if (cliente != null) {
                List<AulaDiaJSON> proximasAulas = agendaService.consultarAgendamentosModalidadesAluno(ctx, Uteis.getDate(inicio, "dd/MM/yyyy"),
                        Uteis.getDate(fim, "dd/MM/yyyy"), matricula, cliente.getEmpresa());
                proximasAulas = Ordenacao.ordenarLista(proximasAulas, "diaDate");
                mm.addAttribute("aulas", proximasAulas);
            } else {
                throw new ServiceException(String.format("Aluno não encontrado na empresa %s com a matrícula: %s", ctx, matricula));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Consultar turmas de um aluno",
            notes = "Consulta todas as turmas nas quais um aluno específico está matriculado ou possui agendamentos. " +
                    "Retorna informações detalhadas sobre cada turma incluindo horários, modalidades, professores e status das aulas. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via código da empresa. " +
                    "<br/><strong>Comportamento:</strong> Busca todas as turmas associadas ao aluno pela matrícula e converte os dados para formato de aula do dia. " +
                    "<br/><strong>Retorno:</strong> Lista de turmas convertidas para formato de aulas com informações completas de agendamento.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso",
                    response = ExemploRespostaConsultarTurmasAluno.class)
    })
    @RequestMapping(value = "{ctx}/consultarTurmasAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarTurmasAluno(@PathVariable String ctx,
                                  @ApiParam(value = "Número da matrícula do aluno para consulta das turmas",
                                          required = true, defaultValue = "12345")
                                  @RequestParam final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            List<AgendaTotalJSON> proximasAulas = agendaService.consultarTurmasAluno(ctx, matricula);
            if (proximasAulas == null) {
                proximasAulas = new ArrayList<AgendaTotalJSON>();
            }
            List<AulaDiaJSON> jsonArray = new ArrayList<AulaDiaJSON>();
            for (AgendaTotalJSON aula : proximasAulas) {
                jsonArray.add(new AulaDiaJSON(aula));
            }
            mm.addAttribute("aulas", jsonArray);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Consultar alunos de uma turma específica",
            notes = "Consulta todos os alunos matriculados em uma turma específica em uma data determinada. " +
                    "Retorna informações básicas dos alunos incluindo nome, matrícula e foto para exibição em listas de presença ou controle de turma. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via código da empresa. " +
                    "<br/><strong>Comportamento:</strong> Busca todos os alunos agendados para o horário de turma especificado na data informada. " +
                    "<br/><strong>Retorno:</strong> Lista de alunos com dados básicos para identificação e controle de presença.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso",
                    response = ExemploRespostaAlunosTurma.class)
    })
    @RequestMapping(value = "{ctx}/alunosTurma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap alunosTurma(@PathVariable String ctx,
                         @ApiParam(value = "Código identificador da empresa (opcional, será obtido automaticamente se não informado)",
                                 defaultValue = "1")
                         @RequestParam(required = false) final Integer empresa,
                         @ApiParam(value = "Código identificador do horário da turma para consulta dos alunos",
                                 required = true, defaultValue = "1001")
                         @RequestParam final Integer codigoHorarioTurma,
                         @ApiParam(value = "Data da aula no formato dd/MM/yyyy para consulta dos alunos agendados",
                                 required = true, defaultValue = "15/02/2024")
                         @RequestParam final String data, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            if (empresa == null) {
                throw new Exception("Não foi possível consultar os alunos da sua Turma. Código da empresa não informado. Por favor, atualize o APP!");
            }
            List<Map<String, String>> alunosTurma = agendaService.fotosAlunosTurma(
                    ctx, empresa, codigoHorarioTurma, data, request);
            mm.addAttribute("alunos", alunosTurma);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Marcar aula para um aluno",
            notes = "Realiza a marcação de uma aula para um aluno específico em um horário de turma determinado. " +
                    "Verifica configurações de inadimplência e pagamento da primeira parcela antes de confirmar a marcação. " +
                    "Pode registrar presença automaticamente se configurado no sistema. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Valida situação financeira do aluno, disponibilidade da turma e realiza a marcação. " +
                    "<br/><strong>Retorno:</strong> Mensagem de confirmação da marcação ou erro detalhando o motivo da falha.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Marcação processada com sucesso",
                    response = ExemploRespostaMarcarAula.class)
    })
    @RequestMapping(value = "{ctx}/marcarAula", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap marcarAula(@PathVariable String ctx,
                        @ApiParam(value = "Número da matrícula do aluno que deseja marcar a aula",
                                required = true, defaultValue = "12345")
                        @RequestParam final Integer matricula,
                        @ApiParam(value = "Código identificador do horário da turma para marcação",
                                required = true, defaultValue = "1001")
                        @RequestParam final Integer codigoHorarioTurma,
                        @ApiParam(value = "Data da aula no formato dd/MM/yyyy para marcação",
                                required = true, defaultValue = "15/02/2024")
                        @RequestParam final String data,
                        @ApiParam(value = "Código do contrato do aluno (opcional, será usado o contrato padrão se não informado)",
                                defaultValue = "5001")
                        @RequestParam(required = false) final Integer contrato) {
        ModelMap mm = new ModelMap();
        try {
            ConfiguracaoSistema cfInacimplente = configuracaoSistemaService.consultarPorTipo(ctx,
                    ConfiguracoesEnum.PROIBIR_MARCAR_AULA_PARCELA_VENCIDA);
            ConfiguracaoSistema cfgProibirMarcarAulaAntesPagamentoPrimeiraParc = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA);
            String result = agendaService.marcarDesmarcar(ctx, matricula, Uteis.getDate(data, "dd/MM/yyyy"),
                    codigoHorarioTurma, true, cfInacimplente.getValorAsBoolean(),
                    cfgProibirMarcarAulaAntesPagamentoPrimeiraParc.getValorAsBoolean(), contrato);
            if (result.startsWith("ERRO")) {
                mm.addAttribute(STATUS_ERRO, result.replaceFirst("ERRO:", ""));
            } else {
                mm.addAttribute(STATUS_SUCESSO, result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Consultar extrato de créditos de treino do aluno",
            notes = "Consulta o histórico completo de operações de créditos de treino de um aluno específico até uma data determinada. " +
                    "Retorna todas as movimentações de crédito e débito, incluindo marcações, desmarcações e operações administrativas. " +
                    "Útil para acompanhar o consumo de créditos e identificar padrões de uso. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Busca todas as operações de crédito do aluno até a data especificada. " +
                    "<br/><strong>Retorno:</strong> Lista ordenada cronologicamente com todas as operações de crédito do aluno.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Extrato consultado com sucesso",
                    response = ExemploRespostaExtrato.class)
    })
    @RequestMapping(value = "{ctx}/extrato", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap extrato(@PathVariable String ctx,
                     @ApiParam(value = "Número da matrícula do aluno para consulta do extrato",
                             required = true, defaultValue = "12345")
                     @RequestParam final Integer matricula,
                     @ApiParam(value = "Data limite para consulta do extrato no formato dd/MM/yyyy HH:mm:ss",
                             required = true, defaultValue = "15/02/2024 23:59:59")
                     @RequestParam final String data) {
        ModelMap mm = new ModelMap();
        try {
            List<ControleCreditoTreinoJSON> extrato = agendaService.consultarExtratoCreditos(ctx, matricula, Uteis.getDate(data, "dd/MM/yyyy HH:mm:ss"));
            if (extrato == null || extrato.isEmpty()) {
                extrato = new ArrayList<ControleCreditoTreinoJSON>();
            }
            mm.addAttribute(RETURN, extrato);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @ApiOperation(value = "Consultar saldo de créditos do aluno",
            notes = "Consulta o saldo atual de créditos de treino disponíveis para um aluno específico. " +
                    "Verifica o modo de consulta configurado no sistema (webservice ou banco de dados) e retorna o saldo atualizado. " +
                    "Essencial para validar se o aluno possui créditos suficientes para marcar novas aulas. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Consulta saldo considerando o contrato específico ou contrato padrão do aluno. " +
                    "<br/><strong>Retorno:</strong> Saldo atual de créditos disponíveis ou mensagem de erro.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Saldo consultado com sucesso",
                    response = ExemploRespostaSaldoAluno.class)
    })
    @RequestMapping(value = "{ctx}/saldoAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap saldoAluno(@PathVariable String ctx,
                        @ApiParam(value = "Número da matrícula do aluno para consulta do saldo",
                                required = true, defaultValue = "12345")
                        @RequestParam final Integer matricula,
                        @ApiParam(value = "Código do contrato específico para consulta (opcional, será usado o contrato padrão se não informado)",
                                defaultValue = "5001")
                        @RequestParam(required = false) final Integer contrato) {
        ModelMap mm = new ModelMap();
        try {
            String result = agendaService.consultarSaldoAlunoVerificandoModoConsulta(ctx, matricula, contrato);
            if (result.startsWith("ERRO:")) {
                mm.addAttribute(STATUS_ERRO, result.replaceAll("ERRO:", ""));
            } else {
                mm.addAttribute(STATUS_SUCESSO, result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @ApiOperation(value = "Consultar saldo de reposições e créditos do aluno para marcação de aulas",
            notes = "Consulta o saldo disponível de reposições de turma e créditos que o aluno possui para agendamento de aulas. " +
                    "Retorna informações específicas para controle de marcação de aulas, incluindo reposições pendentes e créditos ativos.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaSaldoAlunoReporEMarcar.class)
    })
    @RequestMapping(value = "{ctx}/saldoAlunoReporEMarcar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap saldoAlunoRepor(@PathVariable String ctx,
                             @ApiParam(value = "Número da matrícula do aluno para consulta do saldo",
                                     required = true, defaultValue = "12345")
                             @RequestParam final Integer matricula,
                             @ApiParam(value = "Código do contrato específico para consulta (opcional, será usado o contrato padrão se não informado)",
                                     defaultValue = "5001")
                             @RequestParam(required = false) final Integer contrato) {
        ModelMap mm = new ModelMap();
        try {
            String result = agendaService.consultarSaldoAlunoReporEMarcar(ctx, matricula, contrato != null ? contrato : 0);
            if (result.startsWith("ERRO:")) {
                mm.addAttribute(STATUS_ERRO, result.replaceAll("ERRO:", ""));
            } else {
                mm.addAttribute("saldoTurma(reposiçoes)", result.split(";")[0]);
                mm.addAttribute("creditos", result.split(";")[2]);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @ApiOperation(value = "Consultar aulas confirmadas do aluno",
            notes = "Consulta o histórico de aulas confirmadas pelo aluno em um período específico. " +
                    "Retorna informações detalhadas sobre as aulas que o aluno participou, incluindo professor, modalidade e data da aula. " +
                    "Suporta paginação para facilitar a navegação em históricos extensos.",
            tags = "Turmas")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "dataAula,desc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaAulasConfirmadasPaginacao.class)
    })
    @RequestMapping(value = "{ctx}/aulasConfirmadas", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap aulasConfirmadasPorAluno(@PathVariable String ctx,
                                      @ApiParam(value = "Número da matrícula do aluno para consulta das aulas confirmadas",
                                              required = true, defaultValue = "12345")
                                      @RequestParam final Integer matricula,
                                      @ApiParam(value = "Data inicial para filtro do período (formato dd/MM/yyyy)",
                                              defaultValue = "01/01/2024")
                                      @RequestParam(required = false) final String dataInicial,
                                      @ApiParam(value = "Data final para filtro do período (formato dd/MM/yyyy)",
                                              defaultValue = "31/12/2024")
                                      @RequestParam(required = false) final String dataFinal,
                                      @ApiIgnore PaginadorDTO paginadorDTO) {
        ModelMap mm = new ModelMap();
        try {

            mm.addAttribute(STATUS_SUCESSO, agendaService.consultarAulasConfirmadas(ctx, matricula, dataInicial, dataFinal, paginadorDTO));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Marcar interesse do aluno em horário específico de aula",
            notes = "Registra o interesse do aluno em participar de um horário específico de aula. " +
                    "Esta funcionalidade permite que o aluno manifeste interesse em aulas que podem estar lotadas ou indisponíveis, " +
                    "sendo útil para controle de lista de espera e gestão de demanda por horários específicos.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Solicitação processada com sucesso", response = ExemploRespostaMarcarEuQueroHorario.class)
    })
    @RequestMapping(value = "{ctx}/marcarEuQueroHorario", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap marcarEuQueroHorario(@PathVariable String ctx,
                                  @ApiParam(value = "Código identificador do aluno no sistema",
                                          required = true, defaultValue = "1001")
                                  @RequestParam final Integer codigoAluno,
                                  @ApiParam(value = "Código identificador do horário da turma de interesse",
                                          required = true, defaultValue = "2001")
                                  @RequestParam final Integer codigoHorarioTurma,
                                  @ApiParam(value = "Data da aula de interesse no formato dd/MM/yyyy",
                                          required = true, defaultValue = "15/01/2024")
                                  @RequestParam final String data) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico clienteSintetico = clienteService.obterPorCodigo(ctx, codigoAluno);

            if (clienteSintetico == null)
                throw new Exception("Não existe cliente(TreinoWeb) com código=" + codigoAluno);
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            String result = integracaoWS.marcarEuQueroHorario(ctx, clienteSintetico.getCodigoCliente(), codigoHorarioTurma, data);
            registrarLogSolicitacao(ctx, codigoAluno, codigoHorarioTurma, data, "MARCOU");
            regsitrarStatusSolicitacao(mm, result);
        } catch (Exception ex) {
            registraLogErro(mm, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Remover interesse do aluno em horário específico de aula",
            notes = "Remove o interesse previamente manifestado pelo aluno em participar de um horário específico de aula. " +
                    "Esta funcionalidade permite que o aluno cancele sua manifestação de interesse em aulas que podem estar lotadas ou indisponíveis, " +
                    "sendo útil para controle de lista de espera e gestão de demanda por horários específicos. " +
                    "<br/><strong>Autenticação:</strong> Requer autenticação via empresa (ctx). " +
                    "<br/><strong>Permissões:</strong> Acesso liberado para alunos autenticados. " +
                    "<br/><strong>Retorno:</strong> ModelMap com status da operação (sucesso ou erro).",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Solicitação processada com sucesso", response = ExemploRespostaDesmarcarEuQueroHorario.class)
    })
    @RequestMapping(value = "{ctx}/desmarcarEuQueroHorario", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap desmarcarEuQueroHorario(@PathVariable String ctx,
                                     @ApiParam(value = "Código identificador do aluno no sistema",
                                             required = true, defaultValue = "1001")
                                     @RequestParam final Integer codigoAluno,
                                     @ApiParam(value = "Código identificador do horário da turma para remoção do interesse",
                                             required = true, defaultValue = "2001")
                                     @RequestParam final Integer codigoHorarioTurma,
                                     @ApiParam(value = "Data da aula para remoção do interesse no formato dd/MM/yyyy",
                                             required = true, defaultValue = "15/01/2024")
                                     @RequestParam final String data) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico clienteSintetico = clienteService.obterPorCodigo(ctx, codigoAluno);

            if (clienteSintetico == null)
                throw new Exception("Não existe cliente(TreinoWeb) com código=" + codigoAluno);
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            String result = integracaoWS.desmarcarEuQueroHorario(ctx, clienteSintetico.getCodigoCliente(), codigoHorarioTurma, data);
            registrarLogSolicitacao(ctx, codigoAluno, codigoHorarioTurma, data, "DESMARCOU");
            registrarStatusSolicitacao(mm, result);
        } catch (Exception ex) {
            registraLogErro(mm, ex);
        }
        return mm;
    }

    private void registrarStatusSolicitacao(ModelMap mm, String result) {
        if (result.contains("ERRO:"))
            mm.addAttribute(STATUS_ERRO, result);
        else
            mm.addAttribute(STATUS_SUCESSO, "Solicitação enviada.");
    }

    private void registraLogErro(ModelMap mm, Exception ex) {
        mm.addAttribute(STATUS_ERRO, ex.getMessage());
        Logger.getLogger(AlunoTurmaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
    }

    private void registrarLogSolicitacao(@PathVariable String ctx, @RequestParam Integer codigoAluno,
                                         @RequestParam Integer codigoHorarioTurma, @RequestParam String data, String opcao) {
        StringBuilder msg = new StringBuilder();
        msg.append(opcao);
        msg.append(" EU QUERO HORARIO: ctx").append("key:").append(ctx).append(" - códigoAluno:").append(codigoAluno);
        msg.append(" - codigoHorarioTurma:").append(codigoHorarioTurma).append(" - data:").append(data);
        Logger.getLogger(AlunoTurmaJSONControle.class.getName()).log(Level.SEVERE, msg.toString(), "");
    }

    private void regsitrarStatusSolicitacao(ModelMap mm, String result) {
        if (result.contains("ERRO:"))
            mm.addAttribute(STATUS_ERRO, result);
        else
            mm.addAttribute(STATUS_SUCESSO, "Solicitação enviada.");
    }

    @ApiOperation(value = "Consultar turmas disponíveis para o aluno no aplicativo",
            notes = "Consulta as turmas e aulas disponíveis para agendamento pelo aluno dentro de um período específico. " +
                    "Este serviço retorna uma lista de aulas organizadas por data, incluindo informações sobre modalidade, professor, " +
                    "horários, capacidade e disponibilidade de vagas. Ideal para uso em aplicativos móveis onde o aluno precisa " +
                    "visualizar e escolher entre as opções de aulas disponíveis para agendamento. " +
                    "<br/><strong>Autenticação:</strong> Requer autenticação via empresa (ctx). " +
                    "<br/><strong>Permissões:</strong> Acesso liberado para alunos autenticados. " +
                    "<br/><strong>Retorno:</strong> ModelMap com lista de aulas disponíveis ordenadas por data.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaConsultarTurmasDisponiveisApp.class)
    })
    @RequestMapping(value = "{ctx}/app/consultarTurmasDisponiveis", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    ModelMap consultarTurmasDisponiveisApp(@PathVariable String ctx,
                                           @ApiParam(value = "Data de início do período para consulta no formato dd/MM/yyyy",
                                                   required = true, defaultValue = "01/01/2024")
                                           @RequestParam final String inicio,
                                           @ApiParam(value = "Data de fim do período para consulta no formato dd/MM/yyyy",
                                                   required = true, defaultValue = "31/01/2024")
                                           @RequestParam final String fim,
                                           @ApiParam(value = "Código do contrato do aluno para filtrar modalidades específicas",
                                                   defaultValue = "1001")
                                           @RequestParam(required = false) final String contrato,
                                           @ApiParam(value = "Número da matrícula do aluno",
                                                   required = true, defaultValue = "12345")
                                           @RequestParam final Integer matricula,
                                           @ApiParam(value = "Código da empresa (opcional, será usado o da matrícula se não informado)",
                                                   defaultValue = "1")
                                           @RequestParam(required = false) Integer empresa) {
        ModelMap mm = new ModelMap();
        try {
            Integer contratoInt;
            try {
                contratoInt = Integer.valueOf(contrato);
            } catch (Exception e) {
                contratoInt = null;
            }
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx,
                    matricula.toString());
            if (cliente != null) {
                empresa = empresa != null ? empresa : cliente.getEmpresa();
                List<AulaDiaJSON> proximasAulas = agendaService.consultarAgendamentosModalidadesAlunoApp(ctx, Uteis.getDate(inicio, "dd/MM/yyyy"),
                        Uteis.getDate(fim, "dd/MM/yyyy"), matricula, empresa, contratoInt);
                proximasAulas = Ordenacao.ordenarLista(proximasAulas, "diaDate");
                mm.addAttribute("aulas", proximasAulas);
            } else {
                throw new ServiceException(String.format("Aluno não encontrado na empresa %s com a matrícula: %s", ctx, matricula));
            }
        } catch (Exception ex) {
            mm.addAttribute("aulas", new ArrayList<>());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, String.format("Inicio: %s - Fim %s; Contrato %s, Matrícula %d;", inicio, fim, contrato, matricula));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Consultar horários sugeridos para agendamento",
            notes = "Consulta horários disponíveis sugeridos para agendamento de serviços personalizados em uma data específica. " +
                    "Retorna uma lista de sugestões de horários com informações sobre tipo de evento, professor disponível e horários livres. " +
                    "Útil para apresentar opções de agendamento ao aluno baseadas na disponibilidade da academia. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Busca horários livres na agenda considerando a data informada e a empresa. " +
                    "<br/><strong>Retorno:</strong> Lista de horários sugeridos com detalhes do tipo de serviço e professor disponível.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaHorariosSugeridos.class)
    })
    @RequestMapping(value = "{ctx}/horariosSugeridos", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap horariosSugeridos(@PathVariable String ctx,
                               @ApiParam(value = "Data para consulta de horários disponíveis no formato dd/MM/yyyy",
                                       required = true, defaultValue = "15/01/2024")
                               @RequestParam final String data,
                               @ApiParam(value = "Código identificador da empresa para busca de disponibilidades",
                                       required = true, defaultValue = "1")
                               @RequestParam final Integer empresa) {
        ModelMap mm = new ModelMap();
        try {
            List<SugestaoHorarioPersonalJSON> horarioJSONS = avaliacaoService.sugerirHorariosApp(ctx, Calendario.getDataComHoraZerada(Uteis.getDate(data)), empresa);
            mm.addAttribute(RETURN, horarioJSONS);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Consultar disponibilidades de agendamento",
            notes = "Consulta as disponibilidades de agendamento para serviços personalizados em um período específico. " +
                    "Retorna um mapa organizado por data contendo listas de horários disponíveis com informações sobre professores, " +
                    "tipos de serviço e status dos agendamentos. Permite filtrar por período (dia, semana ou mês) e matrícula específica. " +
                    "<br/><br/><strong>Permissões:</strong> Requer permissão AGENDA (RecursoEnum.AGENDA). " +
                    "<br/><strong>Comportamento:</strong> Busca disponibilidades considerando tipos de agendamento ativos e filtros aplicados. " +
                    "<br/><strong>Retorno:</strong> Mapa com chaves de data e valores contendo listas de disponibilidades de agendamento.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaDisponibilidades.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{ctx}/disponibilidades", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidades(@PathVariable String ctx,
                                                                @ApiParam(value = "Código identificador da empresa para busca de disponibilidades",
                                                                        required = true, defaultValue = "1")
                                                                @RequestParam("empresaId") Integer empresaId,
                                                                @ApiParam(value = "Data de referência para consulta no formato dd/MM/yyyy",
                                                                        required = true, defaultValue = "15/01/2024")
                                                                @RequestParam("dia") String dia,
                                                                @ApiParam(value = "Período para filtrar as disponibilidades. " +
                                                                        "<br/><strong>Valores disponíveis:</strong>" +
                                                                        "<ul>" +
                                                                        "<li>DIA - Consulta apenas o dia especificado</li>" +
                                                                        "<li>SEMANA - Consulta toda a semana do dia especificado</li>" +
                                                                        "<li>MES - Consulta todo o mês do dia especificado</li>" +
                                                                        "<li>SERVICO - Filtro por tipo de serviço</li>" +
                                                                        "<li>MODALIDADE - Filtro por modalidade</li>" +
                                                                        "<li>AMBIENTE - Filtro por ambiente</li>" +
                                                                        "<li>DIAS15 - Consulta próximos 15 dias</li>" +
                                                                        "</ul>",
                                                                        required = true, defaultValue = "DIA")
                                                                @RequestParam("periodo") PeriodoFiltrarEnum periodo,
                                                                @ApiParam(value = "Indica se a consulta é específica para o aplicativo de treino",
                                                                        defaultValue = "false")
                                                                @RequestParam(required = false, defaultValue = "false") Boolean appTreino,
                                                                @ApiParam(value = "Número da matrícula do aluno para filtrar disponibilidades específicas",
                                                                        defaultValue = "12345")
                                                                @RequestParam(required = false) Integer matricula,
                                                                HttpServletRequest request) {
        try {
            List<TipoAgendamentoDTO> tipoAgendamentoIds = tipoEventoService.obterTodosAtivosApp(ctx);
            FiltrosAgendamentosDTO filtrosAgendamentos = new FiltrosAgendamentosDTO(tipoAgendamentoIds);
            return ResponseEntityFactory.ok(disponibilidadeService.disponibilidadesApp(ctx, empresaId, Calendario.getDate("dd/MM/yyyy", dia), periodo, filtrosAgendamentos, request, tipoAgendamentoIds.size(), appTreino, matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar disponibilidades agrupadas por comportamento",
            notes = "Consulta as disponibilidades de agendamento organizadas por tipo de comportamento do serviço. " +
                    "Retorna um mapa estruturado onde as disponibilidades são agrupadas primeiro por data e depois por " +
                    "tipo de agendamento (Contato Interpessoal, Prescrição de Treino, Revisão de Treino, etc.). " +
                    "Esta organização facilita a apresentação de serviços categorizados na interface do usuário. " +
                    "<br/><br/><strong>Permissões:</strong> Requer permissão AGENDA (RecursoEnum.AGENDA). " +
                    "<br/><strong>Comportamento:</strong> Agrupa disponibilidades por comportamento para melhor organização visual. " +
                    "<br/><strong>Retorno:</strong> Mapa com estrutura hierárquica: data -> tipo de agendamento -> lista de disponibilidades.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaDisponibilidadesAgrupadasPorComportamento.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{ctx}/v1/disponibilidades", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadesAgrupadasPorComportamento(@PathVariable String ctx,
                                                                                         @ApiParam(value = "Código identificador da empresa para busca de disponibilidades",
                                                                                                 required = true, defaultValue = "1")
                                                                                         @RequestParam("empresaId") Integer empresaId,
                                                                                         @ApiParam(value = "Data de referência para consulta no formato dd/MM/yyyy",
                                                                                                 required = true, defaultValue = "15/01/2024")
                                                                                         @RequestParam("dia") String dia,
                                                                                         @ApiParam(value = "Período para filtrar as disponibilidades. " +
                                                                                                 "<br/><strong>Valores disponíveis:</strong>" +
                                                                                                 "<ul>" +
                                                                                                 "<li>DIA - Consulta apenas o dia especificado</li>" +
                                                                                                 "<li>SEMANA - Consulta toda a semana do dia especificado</li>" +
                                                                                                 "<li>MES - Consulta todo o mês do dia especificado</li>" +
                                                                                                 "<li>SERVICO - Filtro por tipo de serviço</li>" +
                                                                                                 "<li>MODALIDADE - Filtro por modalidade</li>" +
                                                                                                 "<li>AMBIENTE - Filtro por ambiente</li>" +
                                                                                                 "<li>DIAS15 - Consulta próximos 15 dias</li>" +
                                                                                                 "</ul>",
                                                                                                 required = true, defaultValue = "DIA")
                                                                                         @RequestParam("periodo") PeriodoFiltrarEnum periodo,
                                                                                         HttpServletRequest request) {
        try {
            List<TipoAgendamentoDTO> tipoAgendamentoIds = tipoEventoService.obterTodosAtivosApp(ctx);
            FiltrosAgendamentosDTO filtrosAgendamentos = new FiltrosAgendamentosDTO(tipoAgendamentoIds);
            return ResponseEntityFactory.ok(disponibilidadeService.disponibilidadesPorComportamentoApp(ctx, empresaId, Calendario.getDate("dd/MM/yyyy", dia), periodo, filtrosAgendamentos, request, tipoAgendamentoIds.size()));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Criar novo agendamento personalizado",
            notes = "Cria um novo agendamento de serviço personalizado para um aluno específico. " +
                    "Permite agendar serviços como avaliação física, prescrição de treino, revisão de treino, entre outros. " +
                    "O sistema valida conflitos de horário, disponibilidade do professor e regras de negócio específicas. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Valida dados do agendamento, verifica disponibilidade e cria o registro. " +
                    "<br/><strong>Retorno:</strong> Dados completos do agendamento criado incluindo ID gerado e informações do serviço.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Agendamento criado com sucesso", response = ExemploRespostaCriarAgendamento.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/criar-agendamento", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarAgendamento(
            @PathVariable String ctx,
            @ApiParam(value = "Dados do agendamento a ser criado incluindo data, horário, tipo de serviço, professor e aluno")
            @RequestBody AgendamentoPersonalDTO agendamentoDTO,
            HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.criarAgendamentoAlunoPersonal(request, agendamentoDTO.getEmpresa(), agendamentoDTO, ctx));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Remover agendamento existente",
            notes = "Remove um agendamento de serviço personalizado existente do sistema. " +
                    "A operação cancela definitivamente o agendamento, liberando o horário para outros alunos. " +
                    "Verifica regras de negócio para cancelamento e pode gerar notificações automáticas. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Remove o agendamento e atualiza disponibilidades relacionadas. " +
                    "<br/><strong>Retorno:</strong> Confirmação de sucesso da operação de remoção.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Agendamento removido com sucesso", response = ExemploRespostaRemoverAgendamento.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/agendamento/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAgendamento(@PathVariable String ctx,
                                                                  @ApiParam(value = "Código identificador único do agendamento a ser removido",
                                                                          required = true, defaultValue = "123")
                                                                  @PathVariable("id") Integer id) {
        try {
            agendamentoService.removerAgendamentoAlunoPersonal(ctx, id);
            return ResponseEntityFactory.ok("sucesso");
        } catch (HorarioConcomitanteException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Alterar agendamento existente",
            notes = "Altera os dados de um agendamento de serviço personalizado existente. " +
                    "Permite modificar data, horário, tipo de serviço, professor ou observações do agendamento. " +
                    "O sistema valida novamente conflitos de horário e disponibilidade antes de confirmar as alterações. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Valida alterações, verifica disponibilidade e atualiza o registro. " +
                    "<br/><strong>Retorno:</strong> Dados completos do agendamento alterado com as novas informações.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Agendamento alterado com sucesso", response = ExemploRespostaAlterarAgendamento.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/agendamento/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAgendamento(@PathVariable String ctx,
                                                                  @ApiParam(value = "Código identificador único do agendamento a ser alterado",
                                                                          required = true, defaultValue = "123")
                                                                  @PathVariable("id") Integer id,
                                                                  @ApiParam(value = "Novos dados do agendamento incluindo data, horário, tipo de serviço e professor")
                                                                  @RequestBody AgendamentoPersonalDTO agendamentoDTO,
                                                                  HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.alterarAgendamentoAlunoPersonal(request, agendamentoDTO.getEmpresa(), id, agendamentoDTO, ctx));
        } catch (HorarioConcomitanteException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Buscar frequência de treinos dos alunos por carteira do professor",
            notes = "Calcula e retorna a média de frequência de treinos dos alunos que estão na carteira de um professor específico. " +
                    "Permite analisar o engajamento e assiduidade dos alunos atendidos pelo professor em um período determinado. " +
                    "Útil para relatórios de performance e acompanhamento da carteira de clientes do professor. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Calcula estatísticas de frequência baseadas nos agendamentos e presenças. " +
                    "<br/><strong>Retorno:</strong> Dados estatísticos de frequência dos alunos da carteira do professor.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaBuscaFrequenciaAluno.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/treinos-aluno/{codigoProfessor}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaFrequenciaAlunoPorCarteiraProfessor(@PathVariable String ctx,
                                                                                        @ApiParam(value = "Código identificador do professor para consulta da carteira de alunos",
                                                                                                required = true, defaultValue = "25")
                                                                                        @PathVariable("codigoProfessor") Integer codigoProfessor,
                                                                                        @ApiParam(value = "Período em dias para cálculo da frequência (ex: 30 para últimos 30 dias)",
                                                                                                required = true, defaultValue = "30")
                                                                                        @RequestParam("periodo") Integer periodo,
                                                                                        HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.calculaMediaFrequenciaAluno(request, codigoProfessor, ctx, periodo));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao buscar treino", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar frequência de treino e atendimento de um aluno específico",
            notes = "Consulta dados detalhados de frequência de treinos e atendimentos de um aluno específico em um período determinado. " +
                    "Retorna estatísticas de presença, faltas, agendamentos e outras métricas de engajamento do aluno. " +
                    "Útil para acompanhamento individual do progresso e assiduidade do aluno. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Analisa histórico de agendamentos e presenças do aluno no período. " +
                    "<br/><strong>Retorno:</strong> Dados estatísticos detalhados de frequência e atendimento do aluno.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaFrequenciaAluno.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/frequencia/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarFrequenciaAluno(@PathVariable String ctx,
                                                                        @ApiParam(value = "Código identificador único do aluno para consulta de frequência",
                                                                                required = true, defaultValue = "1001")
                                                                        @PathVariable("id") Integer id,
                                                                        @ApiParam(value = "Período em dias para análise da frequência (ex: 30 para últimos 30 dias)",
                                                                                required = true, defaultValue = "30")
                                                                        @RequestParam("periodo") Integer periodo,
                                                                        HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.frequenciaTreinoEAtendimentoAluno(ctx, id, periodo, request));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao buscar frequencias", e);
            return ResponseEntityFactory.erroInterno(e.getStackTrace().toString(), e.getMessage());
        }
    }

    @ApiOperation(value = "Listar objetivos do aluno",
            notes = "Lista todos os objetivos cadastrados para um aluno específico. " +
                    "Permite filtrar por status de alcance e se é objetivo primário. " +
                    "Requer autenticação via empresaId e permissão para visualizar dados do aluno. " +
                    "Retorna lista de objetivos com informações detalhadas incluindo objetivos intermediários.",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de objetivos retornada com sucesso", response = ExemploRespostaListObjetivoAlunoVO.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivos/{matricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> objetivos(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia123")
            @PathVariable String ctx,
            @ApiParam(value = "Número da matrícula do aluno", required = true, defaultValue = "12345")
            @PathVariable Integer matricula,
            @ApiParam(value = "Filtro por status de alcance do objetivo. " +
                    "0 = Não Alcançou, 1 = Alcançou Parcialmente, 2 = Alcançou", defaultValue = "0")
            @RequestParam(required = false) Integer status,
            @ApiParam(value = "Filtro para mostrar apenas objetivos primários (true) ou todos (false/null)", defaultValue = "true")
            @RequestParam(required = false) Boolean primario) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.objetivos(ctx, matricula, status, primario));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter objetivos", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Obter objetivo específico por código",
            notes = "Busca um objetivo específico do aluno através do seu código identificador. " +
                    "Retorna dados completos do objetivo incluindo objetivos intermediários associados. " +
                    "Requer autenticação via empresaId e permissão para visualizar dados do aluno.",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo retornado com sucesso", response = ExemploRespostaObjetivoAlunoVO.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivos/by-codigo/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> objetivo(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia123")
            @PathVariable String ctx,
            @ApiParam(value = "Código identificador único do objetivo", required = true, defaultValue = "123")
            @PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.objetivoPorCodigo(ctx, id));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Criar novo objetivo para aluno",
            notes = "Cria um novo objetivo personalizado para o aluno baseado em um objetivo predefinido. " +
                    "Valida se o objetivo predefinido existe e se o aluno está cadastrado no sistema. " +
                    "Requer autenticação via empresaId e permissão para gerenciar dados do aluno. " +
                    "Retorna o objetivo criado com código identificador gerado.",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo criado com sucesso", response = ExemploRespostaObjetivoAlunoVO.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivos/{matricula}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarObjetivo(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia123")
            @PathVariable String ctx,
            @ApiParam(value = "Número da matrícula do aluno", required = true, defaultValue = "12345")
            @PathVariable Integer matricula,
            @ApiParam(value = "Dados do objetivo a ser criado", required = true)
            @RequestBody ObjetivoAlunoDTO objetivoDTO) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.criarObjetivo(ctx, matricula, objetivoDTO));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Alterar objetivo existente",
            notes = "Atualiza os dados de um objetivo existente do aluno. " +
                    "Valida se o objetivo existe e se o objetivo predefinido informado é válido. " +
                    "Requer autenticação via empresaId e permissão para gerenciar dados do aluno. " +
                    "Retorna o objetivo atualizado com as novas informações.",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo alterado com sucesso", response = ExemploRespostaObjetivoAlunoVO.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivos/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarObjetivo(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia123")
            @PathVariable String ctx,
            @ApiParam(value = "Código identificador único do objetivo a ser alterado", required = true, defaultValue = "123")
            @PathVariable Integer id,
            @ApiParam(value = "Novos dados do objetivo", required = true)
            @RequestBody ObjetivoAlunoDTO objetivoDTO) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.alterarObjetivo(ctx, id, objetivoDTO));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Remover objetivo do aluno",
            notes = "Remove um objetivo principal do aluno do sistema. Esta operação é irreversível e remove completamente o objetivo e todos os seus dados associados. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Remove o objetivo identificado pelo ID fornecido e todos os objetivos intermediários relacionados. " +
                    "<br/><strong>Retorno:</strong> Resposta vazia encapsulada em EnvelopeRespostaDTO indicando sucesso da operação.",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo removido com sucesso",
                    response = ExemploRespostaSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivos/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerObjetivo(@PathVariable String ctx,
                                                               @ApiParam(value = "Código identificador único do objetivo a ser removido", required = true, defaultValue = "123")
                                                               @PathVariable Integer id) {
        try {
            agendamentoService.removerObjetivo(ctx, id);
            return ResponseEntityFactory.ok();
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Criar objetivo intermediário",
            notes = "Cria um novo objetivo intermediário vinculado a um objetivo principal do aluno. Os objetivos intermediários são metas menores que ajudam a alcançar o objetivo principal, " +
                    "permitindo um acompanhamento mais detalhado do progresso do aluno. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Valida os dados fornecidos e cria o objetivo intermediário associado ao objetivo principal especificado. " +
                    "<br/><strong>Retorno:</strong> Dados do objetivo intermediário criado encapsulados em EnvelopeRespostaDTO.",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo intermediário criado com sucesso",
                    response = ExemploRespostaObjetivoIntermediarioAlunoVO.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivosIntermediarios", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarObjetivoIntermediario(@PathVariable String ctx,
                                                                          @ApiParam(value = "Dados do objetivo intermediário a ser criado", required = true)
                                                                          @RequestBody ObjetivoIntermediarioAlunoDTO objetivoIntermediarioDTO) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.criarObjetivoIntermediario(ctx, objetivoIntermediarioDTO));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar objetivo intermediário", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Alterar objetivo intermediário",
            notes = "Atualiza os dados de um objetivo intermediário existente. Permite modificar informações como descrição, datas, status de alcance e categoria do objetivo. " +
                    "Útil para acompanhar o progresso e ajustar metas conforme a evolução do aluno. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Localiza o objetivo intermediário pelo ID e atualiza seus dados com as informações fornecidas. " +
                    "<br/><strong>Retorno:</strong> Dados atualizados do objetivo intermediário encapsulados em EnvelopeRespostaDTO.",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo intermediário alterado com sucesso",
                    response = ExemploRespostaObjetivoIntermediarioAlunoVO.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivosIntermediarios/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarObjetivoIntermediario(@PathVariable String ctx,
                                                                            @ApiParam(value = "Código identificador único do objetivo intermediário a ser alterado", required = true, defaultValue = "456")
                                                                            @PathVariable Integer id,
                                                                            @ApiParam(value = "Dados atualizados do objetivo intermediário", required = true)
                                                                            @RequestBody ObjetivoIntermediarioAlunoDTO objetivoIntermediarioDTO) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.alterarObjetivoIntermediario(ctx, id, objetivoIntermediarioDTO));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar objetivo intermediário", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Remover objetivo intermediário",
            notes = "Remove um objetivo intermediário específico do sistema. Esta operação é irreversível e remove completamente o objetivo intermediário, " +
                    "mas não afeta o objetivo principal ao qual estava vinculado. Útil para limpar objetivos intermediários que não são mais relevantes ou foram alcançados. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Localiza e remove o objetivo intermediário identificado pelo ID fornecido. " +
                    "<br/><strong>Retorno:</strong> Resposta vazia encapsulada em EnvelopeRespostaDTO indicando sucesso da operação.",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo intermediário removido com sucesso",
                    response = ExemploRespostaRemoverObjetivoIntermediario.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivosIntermediarios/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerObjetivoIntermediario(@PathVariable String ctx,
                                                                            @ApiParam(value = "Código identificador único do objetivo intermediário a ser removido", required = true, defaultValue = "456")
                                                                            @PathVariable Integer id) {
        try {
            agendamentoService.removerObjetivoIntermediario(ctx, id);
            return ResponseEntityFactory.ok();
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover objetivo intermediário", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Obter objetivo intermediário específico",
            notes = "Consulta os dados completos de um objetivo intermediário específico pelo seu identificador único. " +
                    "Retorna informações detalhadas incluindo descrição, datas, status de alcance, categoria e vinculação com o objetivo principal. " +
                    "Útil para visualizar o progresso e detalhes de um objetivo intermediário específico. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Busca o objetivo intermediário pelo ID fornecido e retorna seus dados completos. " +
                    "<br/><strong>Retorno:</strong> Dados completos do objetivo intermediário encapsulados em EnvelopeRespostaDTO.",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo intermediário consultado com sucesso",
                    response = ExemploRespostaObjetivoIntermediarioAlunoVO.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivosIntermediarios/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> objetivoIntermediario(@PathVariable String ctx,
                                                                     @ApiParam(value = "Código identificador único do objetivo intermediário a ser consultado", required = true, defaultValue = "456")
                                                                     @PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.objetivoIntermediario(ctx, id));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter objetivo intermediário", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }


    @ApiOperation(value = "Consultar saldo de aulas coletivas do aluno",
            notes = "Consulta o saldo disponível de aulas coletivas (turmas) que o aluno possui para agendamento. " +
                    "Retorna informações sobre créditos de aulas em grupo, modalidades disponíveis e restrições de uso. " +
                    "Essencial para validar se o aluno pode participar de aulas coletivas antes do agendamento. " +
                    "<br/><br/><strong>Permissões:</strong> Requer autenticação via contexto da empresa. " +
                    "<br/><strong>Comportamento:</strong> Consulta saldo específico para aulas coletivas/turmas. " +
                    "<br/><strong>Retorno:</strong> Saldo detalhado de aulas coletivas disponíveis para o aluno.",
            tags = "Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaSaldoAulasColetivas.class)
    })
    @RequestMapping(value = "{ctx}/saldo-aulas-coletivas", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarSaldoAulasColetivas(@PathVariable String ctx,
                                          @ApiParam(value = "Número da matrícula do aluno para consulta do saldo de aulas coletivas",
                                                  required = true, defaultValue = "12345")
                                          @RequestParam final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            SaldoAulaColetivaVO result = agendaService.consultarSaldoAlunoAulasColetivas(ctx, matricula);
            mm.addAttribute(STATUS_SUCESSO, result);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
