package br.com.pacto.controller.json.aluno;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.PerfilAlunoService;
import br.com.pacto.swagger.respostas.aluno.perfil.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/perfil/aluno")
public class PerfilAlunoController {

    private final PerfilAlunoService perfilAlunoService;

    @Autowired
    public PerfilAlunoController(PerfilAlunoService perfilAlunoService) {
        this.perfilAlunoService = perfilAlunoService;
    }

    @ApiOperation(
            value = "Consultar informações consolidadas da avaliação física do aluno",
            notes = "Retorna as informações consolidadas da avaliação física de um aluno, incluindo dados biométricos atuais e históricos, evolução de peso e massa gorda, grupos musculares trabalhados e histórico de dobras cutâneas.",
            tags = "Perfil do Aluno"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaPerfilAlunoAvaliacaoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{matricula}/avaliacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacao(
            @ApiParam(value = "Matrícula do aluno", defaultValue = "12345", required = true)
            @PathVariable("matricula") Integer matricula) {
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.avaliacaoFisica(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter a avaliação do aluno aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar programa de treino atual do aluno",
            notes = "Retorna as informações do programa de treino vigente do aluno, incluindo nome do programa, quantidade de fichas, aulas previstas e realizadas, e percentual de conclusão. O sistema busca o programa ativo na data atual ou o último programa caso não haja nenhum vigente.",
            tags = "Perfil do Aluno"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaProgramaAtualDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{matricula}/programa-atual", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> programaAtual(
            @ApiParam(value = "Matrícula do aluno", defaultValue = "12345", required = true)
            @PathVariable("matricula") Integer matricula) {
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.programaAtual(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter o programa atual do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar ficha de treino do dia do aluno",
            notes = "Retorna as informações da ficha de treino programada para o dia atual do aluno, incluindo identificação, nome da ficha e número de execuções realizadas. Por padrão, busca apenas fichas vigentes, mas pode ser configurado para buscar fichas não vigentes através do parâmetro opcional.",
            tags = "Perfil do Aluno"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaFichaDoDiaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{matricula}/ficha-dia", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> fichaDia(
            @ApiParam(value = "Matrícula do aluno", defaultValue = "12345", required = true)
            @PathVariable("matricula") Integer matricula,
            @ApiParam(value = "Indica se deve buscar fichas não vigentes. Quando true, inclui fichas fora do período de vigência.", defaultValue = "false", required = false)
            @RequestParam(value = "buscarNaoVigente", required = false) Boolean buscarNaoVigente) {
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.fichaDoDia(matricula, buscarNaoVigente));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter a ficha do dia do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar fichas de treino relacionadas do aluno",
            notes = "Retorna as informações das fichas de treino relacionadas ao aluno, incluindo a última ficha executada e a próxima ficha programada no programa vigente. Essas informações auxiliam no acompanhamento da sequência de treinos do aluno.",
            tags = "Perfil do Aluno"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaFichasRelacionadasDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{matricula}/fichas-relacionadas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> fichasRelacionadas(
            @ApiParam(value = "Matrícula do aluno", defaultValue = "12345", required = true)
            @PathVariable("matricula") Integer matricula) {
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.fichasRelacionadas(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter as fichas relacionadas do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar estatísticas de dias de treino do aluno no programa atual",
            notes = "Retorna as estatísticas de frequência de treino do aluno por dia da semana no programa atual. Os dados mostram a distribuição quantitativa dos treinos realizados em cada dia da semana, auxiliando na análise do padrão de frequência do aluno.",
            tags = "Perfil do Aluno"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaDiasQueTreinouProgramaAtualDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{matricula}/dias-treinou-programa-atual", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> diasQueTreinouProgramaAtual(
            @ApiParam(value = "Matrícula do aluno", defaultValue = "12345", required = true)
            @PathVariable("matricula") Integer matricula) {
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.diasQueTreinouProgramaAtual(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os dias que o aluno treinou no programa atual", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar estatísticas de horários de treino do aluno no programa atual",
            notes = "Retorna as estatísticas de horários de treino do aluno no programa atual, mostrando a distribuição dos treinos por período do dia (manhã, tarde e noite). Inclui também a quantidade de dias em que o programa esteve vigente para contextualizar os dados.",
            tags = "Perfil do Aluno"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaHorariosQueTreinouProgramaAtual.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{matricula}/horarios-treinou-programa-atual", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> horariosQueTreinouProgramaAtual(
            @ApiParam(value = "Matrícula do aluno", defaultValue = "12345", required = true)
            @PathVariable("matricula") Integer matricula) {
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.horariosQueTreinouProgramaAtual(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os horários que o aluno treinou no programa atual", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
